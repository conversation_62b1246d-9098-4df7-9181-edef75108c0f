import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;

/// Simple Stripe service for payment processing using Payment Sheet
class StripeService {
  // Direct Cloud Function URL
  static const String _createPaymentIntentUrl =
      'https://us-central1-money-mouthy.cloudfunctions.net/createPaymentIntent';

  /// Create payment intent on backend
  static Future<Map<String, dynamic>?> _createPaymentIntent(
    double amount,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // // Get Firebase ID token for authentication
      // final idToken = await user.getIdToken();

      final response = await http.post(
        Uri.parse(_createPaymentIntentUrl),
        // headers: {
        //   'Content-Type': 'application/json',
        //   'Authorization': 'Bearer $idToken',
        // },
        body: jsonEncode({
          'amount': (amount * 100).round(),
          'currency': 'usd',
          'email': user.email,
        }),
      );

      if (response.statusCode != 200) {
        final errorData = jsonDecode(response.body);
        throw Exception(
          'HTTP ${response.statusCode}: ${errorData['error'] ?? 'Unknown error'}',
        );
      }

      final responseData = jsonDecode(response.body);
      final result = responseData['result'];

      return {
        'ephemeralKey': result['ephemeralKey'],
        'paymentIntent': result['paymentIntent'],
        'customerEmail': result['customerEmail'],
        'customer': result['customer'],
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error creating payment intent: $e');
      }

      if (e.toString().contains('HTTP 401') ||
          e.toString().contains('Unauthorized')) {
        throw Exception('Authentication failed. Please sign in again.');
      }

      throw Exception('Failed to create payment intent: $e');
    }
  }

  /// Process payment using Stripe Payment Sheet
  static Future<bool> processPayment(double amount) async {
    try {
      // Validate amount
      if (amount < 0.50) {
        throw Exception('Minimum amount is \$0.50');
      }

      if (amount > 999999.00) {
        throw Exception('Maximum amount is \$999,999.00');
      }

      // Create payment intent
      final paymentData = await _createPaymentIntent(amount);
      if (paymentData == null) {
        throw Exception('Failed to create payment intent, but HTTP ok');
      }

      // Initialize payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentData['paymentIntent'],
          customerEphemeralKeySecret: paymentData['ephemeralKey'],
          merchantDisplayName: 'Money Mouthy',
          customerId: paymentData['customer'],
          style: ThemeMode.system,

          billingDetails: BillingDetails(
            email: paymentData['customerEmail'],
            name: FirebaseAuth.instance.currentUser?.displayName,
          ),
        ),
      );

      // Present payment sheet
      await Stripe.instance.presentPaymentSheet();
      if (kDebugMode) {
        print('Payment successful');
      }
      // If we reach here, payment was successful
      return true;
    } on StripeException catch (e) {
      if (kDebugMode) {
        print('Stripe error: ${e.error.localizedMessage}');
      }

      // Re-throw cancellation errors so they can be handled appropriately
      if (e.error.code == FailureCode.Canceled) {
        throw Exception('Payment was cancelled');
      }

      throw Exception(e.error.localizedMessage ?? 'Payment failed');
    } catch (e) {
      if (kDebugMode) {
        print('Payment error: $e');
      }
      rethrow;
    }
  }
}
