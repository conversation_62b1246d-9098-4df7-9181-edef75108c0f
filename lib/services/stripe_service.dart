import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

/// Simple Stripe service for payment processing using Payment Sheet
class StripeService {
  static final FirebaseFunctions _functions = FirebaseFunctions.instance;

  /// Create payment intent on backend
  static Future<Map<String, dynamic>?> _createPaymentIntent(
    double amount,
  ) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final callable = _functions.httpsCallable('createPaymentIntent');
      final result = await callable.call({
        'amount': (amount * 100).round(), // Convert to cents
        'currency': 'usd',
      });

      return {
        'clientSecret': result.data['client_secret'],
        'paymentIntentId': result.data['id'],
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error creating payment intent: $e');
      }
      return null;
    }
  }

  /// Process payment using Stripe Payment Sheet
  static Future<bool> processPayment(double amount) async {
    try {
      // Validate amount
      if (amount < 0.50) {
        throw Exception('Minimum amount is \$0.50');
      }

      if (amount > 999999.00) {
        throw Exception('Maximum amount is \$999,999.00');
      }

      // Create payment intent
      final paymentData = await _createPaymentIntent(amount);
      if (paymentData == null) {
        throw Exception('Failed to create payment intent');
      }

      // Initialize payment sheet
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentData['clientSecret'],
          merchantDisplayName: 'Money Mouthy',
          style: ThemeMode.system,
          billingDetails: BillingDetails(
            email: FirebaseAuth.instance.currentUser?.email,
            name: FirebaseAuth.instance.currentUser?.displayName,
          ),
        ),
      );

      // Present payment sheet
      await Stripe.instance.presentPaymentSheet();

      // If we reach here, payment was successful
      return true;
    } on StripeException catch (e) {
      if (kDebugMode) {
        print('Stripe error: ${e.error.localizedMessage}');
      }

      // Re-throw cancellation errors so they can be handled appropriately
      if (e.error.code == FailureCode.Canceled) {
        throw Exception('Payment was cancelled');
      }

      throw Exception(e.error.localizedMessage ?? 'Payment failed');
    } catch (e) {
      if (kDebugMode) {
        print('Payment error: $e');
      }
      rethrow;
    }
  }
}
