import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';

/// Simple helper function to process payment using native Stripe payment sheet
/// The wallet controller's addFunds method handles the Stripe payment sheet internally
Future<void> showStripePaymentSheet({
  required BuildContext context,
  required double amount,
  required VoidCallback onSuccess,
  VoidCallback? onCancel,
}) async {
  final WalletController walletController = Get.find<WalletController>();

  try {
    // The addFunds method now handles the native Stripe payment sheet
    final success = await walletController.addFunds(amount);

    if (success) {
      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Successfully added ${walletController.formatCurrency(amount)}',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
      onSuccess();
    } else {
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              walletController.errorMessage ??
                  'Payment failed. Please try again.',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      onCancel?.call();
    }
  } catch (e) {
    // Handle payment cancellation or errors
    if (context.mounted) {
      String errorMessage = e.toString();

      // Check if payment was cancelled
      if (errorMessage.toLowerCase().contains('cancel')) {
        errorMessage = 'Payment was cancelled.';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage), backgroundColor: Colors.orange),
      );
    }
    onCancel?.call();
  }
}
