const functions = require("firebase-functions");
const stripe = require("stripe")(functions.config().stripe.secret_key);

exports.createPaymentIntent = functions.https.onRequest(async (req, res) => {
  try {
  const { amount, currency = "usd", email } = req.body;
  let customer;
  const customers = await stripe.customers.list({ email, limit: 1 });
  customer = customers.data[0]
    ? customers.data[0]
    : await stripe.customers.create({ email });

  const ephemeralKey = await stripe.ephemeralKeys.create(
    { customer: customer.id },
    { apiVersion: "2020-08-27" }
  );
  const paymentIntent = await stripe.paymentIntents.create({
    amount,
    currency,
    customer: customer.id,
  });

  res.json({
    paymentIntent: paymentIntent.client_secret,
    ephemeralKey: ephemeralKey.secret,
    customer: customer.id,
    customerEmail: customer.email,
    success: true,
  });
  } catch (error) {
    console.error("Error creating payment intent:", error);
    res.status(500).json({
      error: error.message,
      success: false,
    });
  }
});
