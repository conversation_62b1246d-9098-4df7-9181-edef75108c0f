const functions = require("firebase-functions");
const admin = require("firebase-admin");
const stripe = require("stripe")(functions.config().stripe.secret_key);

// Initialize Firebase Admin
admin.initializeApp();

/**
 * Simple Payment Intent Creation Function
 * Creates a Stripe payment intent for wallet funding
 */
exports.createPaymentIntent = functions.https.onCall(async (data, context) => {
  try {
    // Validate user authentication
    if (!context.auth) {
      throw new functions.https.HttpsError(
        "unauthenticated",
        "User must be authenticated"
      );
    }

    const { amount, currency = "usd" } = data;

    // Validate amount
    if (!amount || typeof amount !== "number" || amount <= 0) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Invalid amount"
      );
    }

    // Validate amount limits (minimum $0.50, maximum $999,999)
    if (amount < 50 || amount > 99999900) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Amount must be between $0.50 and $999,999.00"
      );
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount), // Amount in cents
      currency: currency.toLowerCase(),
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        userId: context.auth.uid,
        timestamp: new Date().toISOString(),
        app: "money_mouthy",
      },
    });

    console.log(
      `Payment intent created: ${paymentIntent.id} for user: ${context.auth.uid}, amount: $${amount / 100}`
    );

    return {
      id: paymentIntent.id,
      client_secret: paymentIntent.client_secret,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
    };
  } catch (error) {
    console.error("Error creating payment intent:", error);
    
    // Re-throw Firebase Functions errors
    if (error.code) {
      throw error;
    }
    
    // Handle Stripe errors
    if (error.type) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        `Stripe error: ${error.message}`
      );
    }
    
    throw new functions.https.HttpsError(
      "internal",
      "Unable to create payment intent"
    );
  }
});
